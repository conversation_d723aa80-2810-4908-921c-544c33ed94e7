/**
 * LifeGame 软件保护系统
 * 版本: 1.0.0
 * 功能: 许可证验证、代码保护、用户认证
 */

// 保护系统核心类
class LifeGameProtection {
    constructor() {
        this.isActivated = false;
        this.licenseInfo = null;
        this.hardwareFingerprint = null;
        this.encryptionKey = null;
        this.initTime = Date.now();
        
        // 初始化保护系统
        this.init();
    }

    // 初始化保护系统
    async init() {
        try {
            // 生成硬件指纹
            this.hardwareFingerprint = await this.generateHardwareFingerprint();
            
            // 检查许可证
            await this.checkLicense();
            
            // 启动保护监控
            this.startProtectionMonitoring();
            
            console.log('LifeGame Protection System Initialized');
        } catch (error) {
            console.error('Protection system initialization failed:', error);
            this.handleProtectionFailure();
        }
    }

    // 生成硬件指纹
    async generateHardwareFingerprint() {
        const components = [];
        
        // 屏幕分辨率
        components.push(`${screen.width}x${screen.height}x${screen.colorDepth}`);
        
        // 时区
        components.push(Intl.DateTimeFormat().resolvedOptions().timeZone);
        
        // 语言
        components.push(navigator.language);
        
        // 用户代理（部分）
        components.push(navigator.userAgent.substring(0, 50));
        
        // Canvas指纹
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('LifeGame Fingerprint', 2, 2);
        components.push(canvas.toDataURL().substring(0, 50));
        
        // WebGL指纹
        try {
            const gl = canvas.getContext('webgl');
            const renderer = gl.getParameter(gl.RENDERER);
            components.push(renderer.substring(0, 30));
        } catch (e) {
            components.push('no-webgl');
        }
        
        // 生成指纹哈希
        const fingerprint = await this.simpleHash(components.join('|'));
        return fingerprint;
    }

    // 简单哈希函数
    async simpleHash(str) {
        const encoder = new TextEncoder();
        const data = encoder.encode(str);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 16);
    }

    // 检查许可证
    async checkLicense() {
        const storedLicense = localStorage.getItem('lifegame_license');
        
        if (!storedLicense) {
            throw new Error('No license found');
        }

        try {
            const licenseData = JSON.parse(atob(storedLicense));
            const isValid = await this.validateLicense(licenseData);
            
            if (isValid) {
                this.isActivated = true;
                this.licenseInfo = licenseData;
                this.encryptionKey = await this.deriveEncryptionKey(licenseData.key);
            } else {
                throw new Error('Invalid license');
            }
        } catch (error) {
            throw new Error('License validation failed: ' + error.message);
        }
    }

    // 验证许可证
    async validateLicense(licenseData) {
        // 检查必要字段
        if (!licenseData.key || !licenseData.fingerprint || !licenseData.expiry || !licenseData.signature) {
            return false;
        }

        // 检查硬件指纹
        if (licenseData.fingerprint !== this.hardwareFingerprint) {
            console.warn('Hardware fingerprint mismatch');
            return false;
        }

        // 检查过期时间
        if (Date.now() > licenseData.expiry) {
            console.warn('License expired');
            return false;
        }

        // 验证签名
        const expectedSignature = await this.generateLicenseSignature(licenseData);
        if (licenseData.signature !== expectedSignature) {
            console.warn('License signature invalid');
            return false;
        }

        return true;
    }

    // 生成许可证签名
    async generateLicenseSignature(licenseData) {
        const signatureData = `${licenseData.key}|${licenseData.fingerprint}|${licenseData.expiry}|${this.getSecretSalt()}`;
        return await this.simpleHash(signatureData);
    }

    // 获取密钥盐值（混淆在代码中）
    getSecretSalt() {
        // 这个值应该在代码混淆时进一步隐藏
        const salt = 'LifeGame2024Secret';
        return btoa(salt).split('').reverse().join('');
    }

    // 派生加密密钥
    async deriveEncryptionKey(licenseKey) {
        const keyData = licenseKey + this.hardwareFingerprint + this.getSecretSalt();
        return await this.simpleHash(keyData);
    }

    // 启动保护监控
    startProtectionMonitoring() {
        // 定期检查许可证状态
        setInterval(() => {
            this.periodicLicenseCheck();
        }, 300000); // 5分钟检查一次

        // 监控开发者工具
        this.monitorDevTools();

        // 监控页面完整性
        this.monitorPageIntegrity();
    }

    // 定期许可证检查
    async periodicLicenseCheck() {
        try {
            await this.checkLicense();
        } catch (error) {
            console.warn('Periodic license check failed:', error);
            this.handleProtectionFailure();
        }
    }

    // 监控开发者工具
    monitorDevTools() {
        let devtools = false;
        
        setInterval(() => {
            const start = performance.now();
            debugger;
            const end = performance.now();
            
            if (end - start > 100) {
                if (!devtools) {
                    devtools = true;
                    console.warn('Developer tools detected');
                    this.handleDevToolsDetection();
                }
            } else {
                devtools = false;
            }
        }, 1000);
    }

    // 监控页面完整性
    monitorPageIntegrity() {
        // 检查关键DOM元素
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // 检查是否有可疑的脚本注入
                    mutation.addedNodes.forEach((node) => {
                        if (node.tagName === 'SCRIPT' && !node.hasAttribute('data-lifegame-authorized')) {
                            console.warn('Unauthorized script detected');
                            this.handleTamperingDetection();
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 处理开发者工具检测
    handleDevToolsDetection() {
        // 可以选择警告或限制功能
        if (Math.random() < 0.3) { // 30%概率触发保护
            this.showProtectionWarning('检测到开发者工具，请关闭后继续使用。');
        }
    }

    // 处理篡改检测
    handleTamperingDetection() {
        this.showProtectionWarning('检测到页面被篡改，请刷新页面。');
        setTimeout(() => {
            location.reload();
        }, 3000);
    }

    // 处理保护失败
    handleProtectionFailure() {
        this.isActivated = false;
        this.showLicenseDialog();
    }

    // 显示保护警告
    showProtectionWarning(message) {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
            align-items: center; justify-content: center; color: white;
            font-family: Arial, sans-serif; font-size: 18px; text-align: center;
        `;
        overlay.innerHTML = `<div style="background: #333; padding: 30px; border-radius: 10px; max-width: 400px;">
            <h3>LifeGame 保护系统</h3>
            <p>${message}</p>
            <button onclick="this.parentElement.parentElement.remove()" 
                    style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                确定
            </button>
        </div>`;
        document.body.appendChild(overlay);
    }

    // 显示许可证对话框
    showLicenseDialog() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.9); z-index: 10000; display: flex;
            align-items: center; justify-content: center; color: white;
            font-family: Arial, sans-serif;
        `;
        overlay.innerHTML = `<div style="background: #333; padding: 40px; border-radius: 10px; max-width: 500px; text-align: center;">
            <h2>LifeGame 许可证验证</h2>
            <p>请输入您的许可证密钥以继续使用软件：</p>
            <input type="text" id="licenseKeyInput" placeholder="请输入许可证密钥" 
                   style="width: 100%; padding: 10px; margin: 20px 0; border: none; border-radius: 5px;">
            <div>
                <button onclick="window.lifegameProtection.activateLicense()" 
                        style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                    激活
                </button>
                <button onclick="window.open('https://your-website.com/purchase', '_blank')" 
                        style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    购买许可证
                </button>
            </div>
        </div>`;
        document.body.appendChild(overlay);
    }

    // 激活许可证
    async activateLicense() {
        const keyInput = document.getElementById('licenseKeyInput');
        const licenseKey = keyInput.value.trim();
        
        if (!licenseKey) {
            alert('请输入许可证密钥');
            return;
        }

        try {
            const licenseData = await this.generateLicenseFromKey(licenseKey);
            localStorage.setItem('lifegame_license', btoa(JSON.stringify(licenseData)));
            
            await this.checkLicense();
            
            // 移除许可证对话框
            document.querySelector('[style*="position: fixed"]').remove();
            
            alert('许可证激活成功！');
            location.reload();
        } catch (error) {
            alert('许可证激活失败：' + error.message);
        }
    }

    // 从密钥生成许可证数据
    async generateLicenseFromKey(licenseKey) {
        // 这里应该包含密钥验证逻辑
        // 在实际实现中，可能需要联网验证或使用预设的密钥列表
        
        const validKeys = await this.getValidLicenseKeys();
        const keyInfo = validKeys[licenseKey];
        
        if (!keyInfo) {
            throw new Error('无效的许可证密钥');
        }

        const licenseData = {
            key: licenseKey,
            fingerprint: this.hardwareFingerprint,
            expiry: Date.now() + (keyInfo.days * 24 * 60 * 60 * 1000),
            type: keyInfo.type,
            signature: null
        };

        licenseData.signature = await this.generateLicenseSignature(licenseData);
        return licenseData;
    }

    // 获取有效的许可证密钥（这应该是加密存储的）
    async getValidLicenseKeys() {
        // 在实际部署中，这些密钥应该被加密和混淆
        const encryptedKeys = 'U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwyt';
        
        // 简化的解密逻辑（实际应该更复杂）
        const keys = {
            'LIFEGAME-TRIAL-30': { days: 30, type: 'trial' },
            'LIFEGAME-STANDARD-365': { days: 365, type: 'standard' },
            'LIFEGAME-PREMIUM-365': { days: 365, type: 'premium' },
            'LIFEGAME-LIFETIME': { days: 36500, type: 'lifetime' }
        };
        
        return keys;
    }

    // 检查是否已激活
    isLicenseValid() {
        return this.isActivated && this.licenseInfo && Date.now() < this.licenseInfo.expiry;
    }

    // 获取许可证信息
    getLicenseInfo() {
        return this.licenseInfo;
    }

    // 加密敏感数据
    async encryptData(data) {
        if (!this.encryptionKey) {
            throw new Error('Encryption key not available');
        }
        
        // 简单的XOR加密（实际应该使用更强的加密）
        const dataStr = JSON.stringify(data);
        const key = this.encryptionKey;
        let encrypted = '';
        
        for (let i = 0; i < dataStr.length; i++) {
            const keyChar = key[i % key.length];
            encrypted += String.fromCharCode(dataStr.charCodeAt(i) ^ keyChar.charCodeAt(0));
        }
        
        return btoa(encrypted);
    }

    // 解密敏感数据
    async decryptData(encryptedData) {
        if (!this.encryptionKey) {
            throw new Error('Encryption key not available');
        }
        
        try {
            const encrypted = atob(encryptedData);
            const key = this.encryptionKey;
            let decrypted = '';
            
            for (let i = 0; i < encrypted.length; i++) {
                const keyChar = key[i % key.length];
                decrypted += String.fromCharCode(encrypted.charCodeAt(i) ^ keyChar.charCodeAt(0));
            }
            
            return JSON.parse(decrypted);
        } catch (error) {
            throw new Error('Decryption failed');
        }
    }
}

// 全局保护系统实例
window.lifegameProtection = new LifeGameProtection();

// 导出保护系统
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LifeGameProtection;
}
