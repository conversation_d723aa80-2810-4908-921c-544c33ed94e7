/**
 * LifeGame 商业版本构建脚本
 * 功能：自动化生成受保护的商业版本
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class CommercialBuilder {
    constructor() {
        this.config = {
            version: '1.0.0',
            buildDate: new Date().toISOString(),
            protectionLevel: 'high',
            obfuscationLevel: 'heavy',
            encryptionEnabled: true
        };
        
        this.sourceFiles = [
            'lifegame.html',
            'protection-system.js',
            'code-protection.js',
            'distribution-system.js',
            'user-authentication.js'
        ];
        
        this.outputDir = './dist';
        this.tempDir = './temp';
    }

    // 构建商业版本
    async build() {
        console.log('🚀 开始构建 LifeGame 商业版本...\n');
        
        try {
            // 1. 准备构建环境
            await this.prepareBuildEnvironment();
            
            // 2. 读取源文件
            const sourceCode = await this.readSourceFiles();
            
            // 3. 应用保护措施
            const protectedCode = await this.applyProtection(sourceCode);
            
            // 4. 生成分发包
            await this.generateDistributionPackage(protectedCode);
            
            // 5. 生成许可证密钥
            await this.generateLicenseKeys();
            
            // 6. 创建安装包
            await this.createInstaller();
            
            // 7. 生成文档
            await this.generateDocumentation();
            
            console.log('✅ 商业版本构建完成！');
            console.log(`📦 输出目录: ${this.outputDir}`);
            
        } catch (error) {
            console.error('❌ 构建失败:', error.message);
            process.exit(1);
        }
    }

    // 准备构建环境
    async prepareBuildEnvironment() {
        console.log('📁 准备构建环境...');
        
        // 创建输出目录
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
        
        // 创建临时目录
        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
        }
        
        // 清理旧文件
        this.cleanDirectory(this.outputDir);
        this.cleanDirectory(this.tempDir);
    }

    // 清理目录
    cleanDirectory(dir) {
        if (fs.existsSync(dir)) {
            fs.readdirSync(dir).forEach(file => {
                const filePath = path.join(dir, file);
                if (fs.lstatSync(filePath).isDirectory()) {
                    this.cleanDirectory(filePath);
                    fs.rmdirSync(filePath);
                } else {
                    fs.unlinkSync(filePath);
                }
            });
        }
    }

    // 读取源文件
    async readSourceFiles() {
        console.log('📖 读取源文件...');
        
        const sourceCode = {};
        
        for (const file of this.sourceFiles) {
            if (fs.existsSync(file)) {
                sourceCode[file] = fs.readFileSync(file, 'utf8');
                console.log(`  ✓ ${file}`);
            } else {
                console.warn(`  ⚠️  ${file} 文件不存在`);
            }
        }
        
        return sourceCode;
    }

    // 应用保护措施
    async applyProtection(sourceCode) {
        console.log('🛡️  应用保护措施...');
        
        const protectedCode = {};
        
        for (const [filename, code] of Object.entries(sourceCode)) {
            console.log(`  🔒 保护 ${filename}...`);
            
            let protectedContent = code;
            
            // 1. 代码混淆
            protectedContent = this.obfuscateCode(protectedContent);
            
            // 2. 字符串加密
            protectedContent = this.encryptStrings(protectedContent);
            
            // 3. 添加反调试代码
            protectedContent = this.addAntiDebugCode(protectedContent);
            
            // 4. 内联保护系统
            if (filename === 'lifegame.html') {
                protectedContent = this.inlineProtectionSystem(protectedContent, sourceCode);
            }
            
            protectedCode[filename] = protectedContent;
        }
        
        return protectedCode;
    }

    // 代码混淆
    obfuscateCode(code) {
        // 简单的代码混淆实现
        let obfuscated = code;
        
        // 移除注释
        obfuscated = obfuscated.replace(/\/\*[\s\S]*?\*\//g, '');
        obfuscated = obfuscated.replace(/\/\/.*$/gm, '');
        
        // 压缩空白
        obfuscated = obfuscated.replace(/\s+/g, ' ');
        
        // 变量名混淆（简化版）
        const varMap = new Map();
        let counter = 0;
        
        obfuscated = obfuscated.replace(/\b([a-zA-Z_$][a-zA-Z0-9_$]*)\b/g, (match) => {
            const keywords = ['var', 'let', 'const', 'function', 'if', 'else', 'for', 'while', 'return', 'console', 'window', 'document'];
            if (keywords.includes(match)) {
                return match;
            }
            
            if (!varMap.has(match)) {
                varMap.set(match, `_${counter.toString(36)}`);
                counter++;
            }
            return varMap.get(match);
        });
        
        return obfuscated;
    }

    // 字符串加密
    encryptStrings(code) {
        return code.replace(/'([^']{4,})'/g, (match, str) => {
            const encrypted = Buffer.from(str).toString('base64').split('').reverse().join('');
            return `atob('${encrypted}'.split('').reverse().join(''))`;
        });
    }

    // 添加反调试代码
    addAntiDebugCode(code) {
        const antiDebugCode = `
            // 反调试保护
            setInterval(function(){
                var start = performance.now();
                debugger;
                var end = performance.now();
                if(end - start > 100) {
                    document.body.innerHTML = '<h1>调试器检测</h1><p>请关闭开发者工具</p>';
                }
            }, 1000);
        `;
        
        return antiDebugCode + code;
    }

    // 内联保护系统
    inlineProtectionSystem(htmlCode, sourceCode) {
        // 将保护系统的JavaScript代码内联到HTML中
        let inlinedCode = htmlCode;
        
        // 查找script标签插入位置
        const scriptInsertPoint = inlinedCode.indexOf('</head>');
        
        if (scriptInsertPoint !== -1) {
            let protectionScripts = '<script>\n';
            
            // 内联保护系统代码
            if (sourceCode['protection-system.js']) {
                protectionScripts += '// Protection System\n';
                protectionScripts += sourceCode['protection-system.js'] + '\n\n';
            }
            
            if (sourceCode['code-protection.js']) {
                protectionScripts += '// Code Protection\n';
                protectionScripts += sourceCode['code-protection.js'] + '\n\n';
            }
            
            if (sourceCode['user-authentication.js']) {
                protectionScripts += '// User Authentication\n';
                protectionScripts += sourceCode['user-authentication.js'] + '\n\n';
            }
            
            protectionScripts += '</script>\n';
            
            inlinedCode = inlinedCode.substring(0, scriptInsertPoint) + 
                         protectionScripts + 
                         inlinedCode.substring(scriptInsertPoint);
        }
        
        return inlinedCode;
    }

    // 生成分发包
    async generateDistributionPackage(protectedCode) {
        console.log('📦 生成分发包...');
        
        // 创建主文件
        const mainFile = protectedCode['lifegame.html'];
        fs.writeFileSync(path.join(this.outputDir, 'lifegame-commercial.html'), mainFile);
        
        // 创建配置文件
        const config = {
            version: this.config.version,
            buildDate: this.config.buildDate,
            protection: {
                level: this.config.protectionLevel,
                obfuscation: this.config.obfuscationLevel,
                encryption: this.config.encryptionEnabled
            }
        };
        
        fs.writeFileSync(
            path.join(this.outputDir, 'config.json'), 
            JSON.stringify(config, null, 2)
        );
        
        // 生成校验和
        const checksum = this.calculateChecksum(mainFile);
        fs.writeFileSync(path.join(this.outputDir, 'checksum.txt'), checksum);
        
        console.log('  ✓ 主文件: lifegame-commercial.html');
        console.log('  ✓ 配置文件: config.json');
        console.log('  ✓ 校验和: checksum.txt');
    }

    // 计算校验和
    calculateChecksum(content) {
        return crypto.createHash('sha256').update(content).digest('hex');
    }

    // 生成许可证密钥
    async generateLicenseKeys() {
        console.log('🔑 生成许可证密钥...');
        
        const { LicenseGenerator } = require('./license-generator.js');
        const generator = new LicenseGenerator();
        
        // 生成不同类型的密钥
        const keyTypes = [
            { type: 'trial', count: 100 },
            { type: 'standard', count: 500 },
            { type: 'premium', count: 200 },
            { type: 'lifetime', count: 50 }
        ];
        
        for (const keyType of keyTypes) {
            const keys = generator.generateBatchKeys(keyType.type, keyType.count);
            const csvContent = generator.exportKeyList(keys, 'csv');
            
            fs.writeFileSync(
                path.join(this.outputDir, `${keyType.type}-keys.csv`), 
                csvContent
            );
            
            console.log(`  ✓ ${keyType.type}: ${keyType.count} 个密钥`);
        }
    }

    // 创建安装包
    async createInstaller() {
        console.log('💿 创建安装包...');
        
        const installerScript = `
<!DOCTYPE html>
<html>
<head>
    <title>LifeGame 安装程序</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <h1>LifeGame 生活管理软件</h1>
        <p>版本 ${this.config.version}</p>
    </div>
    
    <div class="step">
        <h3>步骤 1: 下载软件</h3>
        <p>点击下载按钮获取 LifeGame 软件文件</p>
        <button class="button" onclick="downloadSoftware()">下载软件</button>
    </div>
    
    <div class="step">
        <h3>步骤 2: 获取许可证</h3>
        <p>购买许可证密钥以激活软件功能</p>
        <button class="button" onclick="window.open('https://your-website.com/purchase')">购买许可证</button>
    </div>
    
    <div class="step">
        <h3>步骤 3: 激活软件</h3>
        <p>打开软件并输入您的许可证密钥进行激活</p>
    </div>
    
    <script>
        function downloadSoftware() {
            // 这里应该实现实际的下载逻辑
            alert('请联系客服获取下载链接');
        }
    </script>
</body>
</html>
        `;
        
        fs.writeFileSync(path.join(this.outputDir, 'installer.html'), installerScript);
        console.log('  ✓ 安装程序: installer.html');
    }

    // 生成文档
    async generateDocumentation() {
        console.log('📚 生成文档...');
        
        const readme = `# LifeGame 商业版

## 版本信息
- 版本: ${this.config.version}
- 构建日期: ${this.config.buildDate}
- 保护级别: ${this.config.protectionLevel}

## 文件说明
- \`lifegame-commercial.html\`: 主程序文件
- \`config.json\`: 配置文件
- \`checksum.txt\`: 文件校验和
- \`installer.html\`: 安装程序
- \`*-keys.csv\`: 许可证密钥文件

## 部署说明
1. 将 \`lifegame-commercial.html\` 部署到 Web 服务器
2. 配置许可证验证系统
3. 设置用户注册和购买流程
4. 提供技术支持服务

## 安全注意事项
- 保护许可证密钥文件的安全
- 定期更新保护机制
- 监控软件使用情况
- 及时响应安全问题

## 技术支持
如有问题请联系技术支持团队。
        `;
        
        fs.writeFileSync(path.join(this.outputDir, 'README.md'), readme);
        
        // 复制商业化指南
        if (fs.existsSync('COMMERCIALIZATION-GUIDE.md')) {
            fs.copyFileSync('COMMERCIALIZATION-GUIDE.md', path.join(this.outputDir, 'COMMERCIALIZATION-GUIDE.md'));
        }
        
        console.log('  ✓ README.md');
        console.log('  ✓ COMMERCIALIZATION-GUIDE.md');
    }

    // 显示构建统计
    showBuildStats() {
        console.log('\n📊 构建统计:');
        
        const files = fs.readdirSync(this.outputDir);
        files.forEach(file => {
            const filePath = path.join(this.outputDir, file);
            const stats = fs.statSync(filePath);
            const size = (stats.size / 1024).toFixed(2);
            console.log(`  ${file}: ${size} KB`);
        });
    }
}

// 命令行接口
if (require.main === module) {
    const builder = new CommercialBuilder();
    
    // 解析命令行参数
    const args = process.argv.slice(2);
    args.forEach(arg => {
        if (arg.startsWith('--version=')) {
            builder.config.version = arg.split('=')[1];
        } else if (arg.startsWith('--protection=')) {
            builder.config.protectionLevel = arg.split('=')[1];
        } else if (arg.startsWith('--obfuscation=')) {
            builder.config.obfuscationLevel = arg.split('=')[1];
        }
    });
    
    // 开始构建
    builder.build().then(() => {
        builder.showBuildStats();
    }).catch(error => {
        console.error('构建失败:', error);
        process.exit(1);
    });
}

module.exports = CommercialBuilder;
