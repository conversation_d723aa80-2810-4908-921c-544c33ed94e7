/**
 * LifeGame 分发系统
 * 功能: 安全分发、用户激活、版本管理
 */

class DistributionSystem {
    constructor() {
        this.distributionConfig = {
            version: '1.0.0',
            buildDate: Date.now(),
            distributionId: this.generateDistributionId(),
            encryptionEnabled: true
        };
        
        this.init();
    }

    // 初始化分发系统
    init() {
        this.setupVersionCheck();
        this.setupDistributionTracking();
        this.setupUpdateMechanism();
    }

    // 生成分发ID
    generateDistributionId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2, 10);
        return `LG-${timestamp}-${random}`.toUpperCase();
    }

    // 版本检查
    setupVersionCheck() {
        // 检查是否为最新版本
        const currentVersion = this.distributionConfig.version;
        const storedVersion = localStorage.getItem('lifegame_version');
        
        if (storedVersion && storedVersion !== currentVersion) {
            this.handleVersionUpdate(storedVersion, currentVersion);
        }
        
        localStorage.setItem('lifegame_version', currentVersion);
    }

    // 处理版本更新
    handleVersionUpdate(oldVersion, newVersion) {
        console.log(`LifeGame updated from ${oldVersion} to ${newVersion}`);
        
        // 显示更新通知
        this.showUpdateNotification(oldVersion, newVersion);
        
        // 执行数据迁移（如果需要）
        this.performDataMigration(oldVersion, newVersion);
    }

    // 显示更新通知
    showUpdateNotification(oldVersion, newVersion) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed; top: 20px; right: 20px; width: 300px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 20px; border-radius: 10px; z-index: 10000;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3); font-family: Arial, sans-serif;
        `;
        notification.innerHTML = `
            <h4 style="margin: 0 0 10px 0;">🎉 LifeGame 已更新</h4>
            <p style="margin: 0 0 10px 0; font-size: 14px;">
                版本 ${oldVersion} → ${newVersion}
            </p>
            <button onclick="this.parentElement.remove()" 
                    style="background: rgba(255,255,255,0.2); color: white; border: none; 
                           padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                确定
            </button>
        `;
        document.body.appendChild(notification);
        
        // 5秒后自动消失
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    // 数据迁移
    performDataMigration(oldVersion, newVersion) {
        try {
            const migrationRules = this.getMigrationRules();
            const applicableRules = migrationRules.filter(rule => 
                this.compareVersions(oldVersion, rule.fromVersion) >= 0 &&
                this.compareVersions(newVersion, rule.toVersion) <= 0
            );
            
            applicableRules.forEach(rule => {
                console.log(`Applying migration rule: ${rule.description}`);
                rule.migrate();
            });
        } catch (error) {
            console.error('Data migration failed:', error);
        }
    }

    // 获取迁移规则
    getMigrationRules() {
        return [
            {
                fromVersion: '0.9.0',
                toVersion: '1.0.0',
                description: 'Migrate to new data structure',
                migrate: () => {
                    // 示例迁移逻辑
                    const oldData = localStorage.getItem('lifegame_data_old');
                    if (oldData) {
                        const newData = this.convertOldDataFormat(JSON.parse(oldData));
                        localStorage.setItem('lifegame_data', JSON.stringify(newData));
                        localStorage.removeItem('lifegame_data_old');
                    }
                }
            }
        ];
    }

    // 版本比较
    compareVersions(version1, version2) {
        const v1parts = version1.split('.').map(Number);
        const v2parts = version2.split('.').map(Number);
        
        for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
            const v1part = v1parts[i] || 0;
            const v2part = v2parts[i] || 0;
            
            if (v1part > v2part) return 1;
            if (v1part < v2part) return -1;
        }
        return 0;
    }

    // 分发跟踪
    setupDistributionTracking() {
        // 记录分发信息
        const distributionInfo = {
            id: this.distributionConfig.distributionId,
            version: this.distributionConfig.version,
            installDate: Date.now(),
            lastAccess: Date.now()
        };
        
        localStorage.setItem('lifegame_distribution', JSON.stringify(distributionInfo));
        
        // 定期更新最后访问时间
        setInterval(() => {
            this.updateLastAccess();
        }, 60000); // 每分钟更新一次
    }

    // 更新最后访问时间
    updateLastAccess() {
        try {
            const distributionInfo = JSON.parse(localStorage.getItem('lifegame_distribution') || '{}');
            distributionInfo.lastAccess = Date.now();
            localStorage.setItem('lifegame_distribution', JSON.stringify(distributionInfo));
        } catch (error) {
            console.error('Failed to update last access time:', error);
        }
    }

    // 更新机制
    setupUpdateMechanism() {
        // 检查更新（可选功能）
        this.checkForUpdates();
    }

    // 检查更新
    async checkForUpdates() {
        try {
            // 这里可以实现在线更新检查
            // 为了保持离线特性，这里只是示例
            const updateInfo = await this.getUpdateInfo();
            
            if (updateInfo && this.compareVersions(updateInfo.version, this.distributionConfig.version) > 0) {
                this.showUpdateAvailable(updateInfo);
            }
        } catch (error) {
            console.log('Update check failed (offline mode)');
        }
    }

    // 获取更新信息（模拟）
    async getUpdateInfo() {
        // 在实际实现中，这里可以连接到更新服务器
        // 为了保持离线特性，返回null
        return null;
    }

    // 显示可用更新
    showUpdateAvailable(updateInfo) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed; bottom: 20px; right: 20px; width: 350px;
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white; padding: 20px; border-radius: 10px; z-index: 10000;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3); font-family: Arial, sans-serif;
        `;
        notification.innerHTML = `
            <h4 style="margin: 0 0 10px 0;">🚀 新版本可用</h4>
            <p style="margin: 0 0 15px 0; font-size: 14px;">
                LifeGame ${updateInfo.version} 现已可用<br>
                <small>${updateInfo.description}</small>
            </p>
            <div>
                <button onclick="window.open('${updateInfo.downloadUrl}', '_blank')" 
                        style="background: rgba(255,255,255,0.9); color: #333; border: none; 
                               padding: 8px 16px; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                    下载更新
                </button>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="background: rgba(255,255,255,0.2); color: white; border: none; 
                               padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                    稍后提醒
                </button>
            </div>
        `;
        document.body.appendChild(notification);
    }

    // 获取分发信息
    getDistributionInfo() {
        return {
            ...this.distributionConfig,
            distributionInfo: JSON.parse(localStorage.getItem('lifegame_distribution') || '{}')
        };
    }

    // 生成分发包
    static generateDistributionPackage(sourceFiles, config = {}) {
        const defaultConfig = {
            version: '1.0.0',
            encryptionEnabled: true,
            obfuscationLevel: 'medium',
            includeSourceMaps: false
        };
        
        const finalConfig = { ...defaultConfig, ...config };
        
        return {
            config: finalConfig,
            files: sourceFiles.map(file => ({
                path: file.path,
                content: this.processFileForDistribution(file.content, finalConfig),
                checksum: this.calculateChecksum(file.content)
            })),
            manifest: this.generateManifest(sourceFiles, finalConfig)
        };
    }

    // 处理分发文件
    static processFileForDistribution(content, config) {
        let processedContent = content;
        
        // 代码混淆
        if (config.obfuscationLevel !== 'none') {
            processedContent = this.obfuscateCode(processedContent, config.obfuscationLevel);
        }
        
        // 加密敏感内容
        if (config.encryptionEnabled) {
            processedContent = this.encryptSensitiveContent(processedContent);
        }
        
        // 移除注释和调试代码
        processedContent = this.removeDebugCode(processedContent);
        
        return processedContent;
    }

    // 代码混淆
    static obfuscateCode(code, level) {
        // 根据混淆级别应用不同的混淆策略
        switch (level) {
            case 'light':
                return this.lightObfuscation(code);
            case 'medium':
                return this.mediumObfuscation(code);
            case 'heavy':
                return this.heavyObfuscation(code);
            default:
                return code;
        }
    }

    // 轻度混淆
    static lightObfuscation(code) {
        // 移除多余的空白和注释
        return code
            .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
            .replace(/\/\/.*$/gm, '') // 移除行注释
            .replace(/\s+/g, ' ') // 压缩空白
            .trim();
    }

    // 中度混淆
    static mediumObfuscation(code) {
        let obfuscated = this.lightObfuscation(code);
        
        // 字符串编码
        obfuscated = obfuscated.replace(/'([^']+)'/g, (match, str) => {
            if (str.length > 3) {
                return `atob('${btoa(str)}')`;
            }
            return match;
        });
        
        return obfuscated;
    }

    // 重度混淆
    static heavyObfuscation(code) {
        let obfuscated = this.mediumObfuscation(code);
        
        // 变量名混淆（简化版）
        const varMap = new Map();
        let counter = 0;
        
        obfuscated = obfuscated.replace(/\b([a-zA-Z_$][a-zA-Z0-9_$]*)\b/g, (match) => {
            if (['var', 'let', 'const', 'function', 'if', 'else', 'for', 'while', 'return'].includes(match)) {
                return match;
            }
            
            if (!varMap.has(match)) {
                varMap.set(match, `_${counter.toString(36)}`);
                counter++;
            }
            return varMap.get(match);
        });
        
        return obfuscated;
    }

    // 加密敏感内容
    static encryptSensitiveContent(code) {
        // 加密许可证相关的字符串
        const sensitivePatterns = [
            /(['"`])LifeGame.*?\1/g,
            /(['"`])license.*?\1/gi,
            /(['"`])protection.*?\1/gi
        ];
        
        sensitivePatterns.forEach(pattern => {
            code = code.replace(pattern, (match) => {
                const encrypted = btoa(match).split('').reverse().join('');
                return `atob('${encrypted}'.split('').reverse().join(''))`;
            });
        });
        
        return code;
    }

    // 移除调试代码
    static removeDebugCode(code) {
        return code
            .replace(/console\.(log|debug|info)\([^)]*\);?/g, '') // 移除console输出
            .replace(/debugger;?/g, '') // 移除debugger语句
            .replace(/\/\*\s*DEBUG[\s\S]*?\*\//g, '') // 移除DEBUG注释块
            .replace(/\/\/\s*DEBUG.*$/gm, ''); // 移除DEBUG行注释
    }

    // 计算校验和
    static calculateChecksum(content) {
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString(16);
    }

    // 生成清单文件
    static generateManifest(sourceFiles, config) {
        return {
            version: config.version,
            buildDate: Date.now(),
            files: sourceFiles.map(file => ({
                path: file.path,
                size: file.content.length,
                checksum: this.calculateChecksum(file.content)
            })),
            config: config
        };
    }
}

// 分发包生成器
class DistributionPackager {
    static async createPackage(projectPath, outputPath, config = {}) {
        console.log('Creating LifeGame distribution package...');
        
        // 读取源文件
        const sourceFiles = await this.readSourceFiles(projectPath);
        
        // 生成分发包
        const distributionPackage = DistributionSystem.generateDistributionPackage(sourceFiles, config);
        
        // 写入输出文件
        await this.writeDistributionPackage(distributionPackage, outputPath);
        
        console.log('Distribution package created successfully!');
        return distributionPackage;
    }

    static async readSourceFiles(projectPath) {
        // 这里应该实现文件读取逻辑
        // 在浏览器环境中，这个功能有限
        return [
            { path: 'lifegame.html', content: '/* Main HTML file */' },
            { path: 'protection-system.js', content: '/* Protection system */' },
            { path: 'code-protection.js', content: '/* Code protection */' }
        ];
    }

    static async writeDistributionPackage(package, outputPath) {
        // 在浏览器环境中，可以生成下载链接
        const packageJson = JSON.stringify(package, null, 2);
        const blob = new Blob([packageJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'lifegame-distribution-package.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// 初始化分发系统
window.distributionSystem = new DistributionSystem();

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DistributionSystem, DistributionPackager };
}
