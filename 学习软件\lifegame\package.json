{"name": "lifegame-commercial", "version": "1.0.0", "description": "LifeGame 生活管理软件 - 商业版", "main": "lifegame.html", "scripts": {"build": "node build-commercial.js", "build:dev": "node build-commercial.js --protection=low --obfuscation=light", "build:prod": "node build-commercial.js --protection=high --obfuscation=heavy", "generate-keys": "node license-generator.js", "test-protection": "node test-protection.js", "serve": "http-server . -p 8080", "package": "npm run build:prod && npm run create-package", "create-package": "node create-package.js"}, "keywords": ["lifegame", "life-management", "productivity", "personal-development", "habit-tracking"], "author": "LifeGame Team", "license": "Commercial", "devDependencies": {"http-server": "^14.1.1"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/lifegame-commercial.git"}, "bugs": {"url": "https://github.com/your-username/lifegame-commercial/issues"}, "homepage": "https://lifegame.com", "config": {"protection": {"level": "high", "obfuscation": "heavy", "encryption": true}, "licensing": {"trialDays": 30, "requireActivation": true}}}