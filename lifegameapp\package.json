{"name": "lifegame-commercial", "version": "1.0.0", "description": "LifeGame 生活管理软件 - 商业版", "main": "lifegame.html", "scripts": {"build": "node build-commercial.js", "build:dev": "node build-commercial.js --protection=low", "build:prod": "node build-commercial.js --protection=high", "generate-keys": "node -e \"const {LicenseGeneratorDemo} = require('./license-generator.js'); LicenseGeneratorDemo.runDemo();\"", "serve": "python -m http.server 8080 || python3 -m http.server 8080", "test": "echo \"打开 dist/installer.html 进行测试\"", "clean": "rmdir /s /q dist 2>nul || rm -rf dist", "help": "echo \"可用命令: npm run build, npm run serve, npm run test\""}, "keywords": ["lifegame", "life-management", "productivity", "personal-development", "habit-tracking", "commercial"], "author": "LifeGame Team", "license": "Commercial", "engines": {"node": ">=12.0.0"}, "config": {"protection": {"level": "high", "obfuscation": "heavy", "encryption": true}, "licensing": {"trialDays": 30, "requireActivation": true}}}