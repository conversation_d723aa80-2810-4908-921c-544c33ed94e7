/**
 * LifeGame 商业版本构建脚本
 * 功能：自动化生成受保护的商业版本
 */

const fs = require('fs');
const path = require('path');

class CommercialBuilder {
    constructor() {
        this.config = {
            version: '1.0.0',
            buildDate: new Date().toISOString(),
            protectionLevel: 'high',
            obfuscationLevel: 'heavy',
            encryptionEnabled: true
        };
        
        this.sourceFiles = [
            'lifegame.html',
            'protection-system.js',
            'license-generator.js'
        ];
        
        this.outputDir = './dist';
        this.tempDir = './temp';
    }

    // 构建商业版本
    async build() {
        console.log('🚀 开始构建 LifeGame 商业版本...\n');
        
        try {
            // 1. 准备构建环境
            await this.prepareBuildEnvironment();
            
            // 2. 读取源文件
            const sourceCode = await this.readSourceFiles();
            
            // 3. 应用保护措施
            const protectedCode = await this.applyProtection(sourceCode);
            
            // 4. 生成分发包
            await this.generateDistributionPackage(protectedCode);
            
            // 5. 生成许可证密钥
            await this.generateLicenseKeys();
            
            // 6. 创建安装包
            await this.createInstaller();
            
            // 7. 生成文档
            await this.generateDocumentation();
            
            console.log('✅ 商业版本构建完成！');
            console.log(`📦 输出目录: ${this.outputDir}`);
            this.showBuildStats();
            
        } catch (error) {
            console.error('❌ 构建失败:', error.message);
            process.exit(1);
        }
    }

    // 准备构建环境
    async prepareBuildEnvironment() {
        console.log('📁 准备构建环境...');
        
        // 创建输出目录
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
        
        // 创建临时目录
        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
        }
        
        // 清理旧文件
        this.cleanDirectory(this.outputDir);
    }

    // 清理目录
    cleanDirectory(dir) {
        if (fs.existsSync(dir)) {
            fs.readdirSync(dir).forEach(file => {
                const filePath = path.join(dir, file);
                if (fs.lstatSync(filePath).isDirectory()) {
                    this.cleanDirectory(filePath);
                    fs.rmdirSync(filePath);
                } else {
                    fs.unlinkSync(filePath);
                }
            });
        }
    }

    // 读取源文件
    async readSourceFiles() {
        console.log('📖 读取源文件...');
        
        const sourceCode = {};
        
        for (const file of this.sourceFiles) {
            if (fs.existsSync(file)) {
                sourceCode[file] = fs.readFileSync(file, 'utf8');
                console.log(`  ✓ ${file}`);
            } else {
                console.warn(`  ⚠️  ${file} 文件不存在`);
            }
        }
        
        return sourceCode;
    }

    // 应用保护措施
    async applyProtection(sourceCode) {
        console.log('🛡️  应用保护措施...');
        
        const protectedCode = {};
        
        for (const [filename, code] of Object.entries(sourceCode)) {
            console.log(`  🔒 保护 ${filename}...`);
            
            let protectedContent = code;
            
            // 1. 内联保护系统
            if (filename === 'lifegame.html') {
                protectedContent = this.inlineProtectionSystem(protectedContent, sourceCode);
            }
            
            // 2. 代码混淆
            if (filename.endsWith('.js')) {
                protectedContent = this.obfuscateCode(protectedContent);
            }
            
            // 3. 字符串加密
            protectedContent = this.encryptStrings(protectedContent);
            
            // 4. 添加反调试代码
            protectedContent = this.addAntiDebugCode(protectedContent);
            
            protectedCode[filename] = protectedContent;
        }
        
        return protectedCode;
    }

    // 内联保护系统
    inlineProtectionSystem(htmlCode, sourceCode) {
        // 将保护系统的JavaScript代码内联到HTML中
        let inlinedCode = htmlCode;
        
        // 查找script标签插入位置
        const scriptInsertPoint = inlinedCode.indexOf('</head>');
        
        if (scriptInsertPoint !== -1) {
            let protectionScripts = '\n    <!-- 软件保护系统 -->\n    <script>\n';
            
            // 内联保护系统代码
            if (sourceCode['protection-system.js']) {
                protectionScripts += '        // LifeGame Protection System\n';
                protectionScripts += '        ' + sourceCode['protection-system.js'].replace(/\n/g, '\n        ') + '\n\n';
            }
            
            protectionScripts += '    </script>\n';
            
            inlinedCode = inlinedCode.substring(0, scriptInsertPoint) + 
                         protectionScripts + 
                         inlinedCode.substring(scriptInsertPoint);
        }
        
        return inlinedCode;
    }

    // 代码混淆
    obfuscateCode(code) {
        // 简单的代码混淆实现
        let obfuscated = code;
        
        // 移除注释
        obfuscated = obfuscated.replace(/\/\*[\s\S]*?\*\//g, '');
        obfuscated = obfuscated.replace(/\/\/.*$/gm, '');
        
        // 压缩空白
        obfuscated = obfuscated.replace(/\s+/g, ' ');
        
        return obfuscated;
    }

    // 字符串加密
    encryptStrings(code) {
        return code.replace(/'([^']{6,})'/g, (match, str) => {
            if (str.includes('LifeGame') || str.includes('license') || str.includes('protection')) {
                const encrypted = Buffer.from(str).toString('base64').split('').reverse().join('');
                return `atob('${encrypted}'.split('').reverse().join(''))`;
            }
            return match;
        });
    }

    // 添加反调试代码
    addAntiDebugCode(code) {
        const antiDebugCode = `
            // 反调试保护
            (function(){
                setInterval(function(){
                    var start = performance.now();
                    debugger;
                    var end = performance.now();
                    if(end - start > 100) {
                        console.clear();
                        document.body.style.display = 'none';
                        alert('请关闭开发者工具');
                        location.reload();
                    }
                }, 1000);
            })();
        `;
        
        return code + antiDebugCode;
    }

    // 生成分发包
    async generateDistributionPackage(protectedCode) {
        console.log('📦 生成分发包...');
        
        // 创建主文件
        const mainFile = protectedCode['lifegame.html'];
        fs.writeFileSync(path.join(this.outputDir, 'lifegame-commercial.html'), mainFile);
        
        // 创建配置文件
        const config = {
            version: this.config.version,
            buildDate: this.config.buildDate,
            protection: {
                level: this.config.protectionLevel,
                obfuscation: this.config.obfuscationLevel,
                encryption: this.config.encryptionEnabled
            }
        };
        
        fs.writeFileSync(
            path.join(this.outputDir, 'config.json'), 
            JSON.stringify(config, null, 2)
        );
        
        console.log('  ✓ 主文件: lifegame-commercial.html');
        console.log('  ✓ 配置文件: config.json');
    }

    // 生成许可证密钥
    async generateLicenseKeys() {
        console.log('🔑 生成许可证密钥...');
        
        // 使用内置的许可证生成器
        const { LicenseGenerator } = require('./license-generator.js');
        const generator = new LicenseGenerator();
        
        // 生成不同类型的密钥
        const keyTypes = [
            { type: 'trial', count: 50 },
            { type: 'standard', count: 100 },
            { type: 'premium', count: 50 },
            { type: 'lifetime', count: 20 }
        ];
        
        for (const keyType of keyTypes) {
            const keys = generator.generateBatchKeys(keyType.type, keyType.count);
            const csvContent = generator.exportKeyList(keys, 'csv');
            
            fs.writeFileSync(
                path.join(this.outputDir, `${keyType.type}-keys.csv`), 
                csvContent
            );
            
            console.log(`  ✓ ${keyType.type}: ${keyType.count} 个密钥`);
        }

        // 生成测试密钥文件
        const testKeys = generator.generateTestKeys();
        const testKeysContent = Object.entries(testKeys).map(([type, key]) => 
            `${type.toUpperCase()}: ${key}`
        ).join('\n');
        
        fs.writeFileSync(
            path.join(this.outputDir, 'test-keys.txt'), 
            testKeysContent
        );
        
        console.log('  ✓ 测试密钥: test-keys.txt');
    }

    // 创建安装包
    async createInstaller() {
        console.log('💿 创建安装包...');
        
        const installerScript = `<!DOCTYPE html>
<html>
<head>
    <title>LifeGame 安装程序</title>
    <meta charset="UTF-8">
    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .header { text-align: center; margin-bottom: 40px; }
        .step { 
            margin: 30px 0; 
            padding: 20px; 
            background: rgba(255,255,255,0.1); 
            border-radius: 10px; 
        }
        .button { 
            background: linear-gradient(45deg, #28a745, #20c997); 
            color: white; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 16px;
            transition: transform 0.2s;
        }
        .button:hover { transform: translateY(-2px); }
        .test-keys {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 LifeGame 生活管理软件</h1>
            <p>版本 ${this.config.version} | 构建日期: ${new Date().toLocaleDateString()}</p>
        </div>
        
        <div class="step">
            <h3>📥 步骤 1: 获取软件</h3>
            <p>LifeGame 商业版已经准备就绪，包含完整的保护系统和许可证验证。</p>
            <button class="button" onclick="downloadSoftware()">打开软件</button>
        </div>
        
        <div class="step">
            <h3>🔑 步骤 2: 测试许可证</h3>
            <p>您可以使用以下测试密钥来体验不同版本的功能：</p>
            <div class="test-keys">
                <strong>试用版 (30天):</strong> TEST-TRIAL-2024<br>
                <strong>标准版 (1年):</strong> TEST-STANDARD-2024<br>
                <strong>高级版 (1年):</strong> TEST-PREMIUM-2024<br>
                <strong>终身版:</strong> TEST-LIFETIME-2024
            </div>
        </div>
        
        <div class="step">
            <h3>🛒 步骤 3: 购买正式许可证</h3>
            <p>测试满意后，您可以购买正式的许可证密钥。</p>
            <button class="button" onclick="showPricing()">查看价格</button>
        </div>
        
        <div class="step">
            <h3>🚀 步骤 4: 激活软件</h3>
            <p>打开软件后，输入您的许可证密钥进行激活，即可享受完整功能。</p>
        </div>
    </div>
    
    <script>
        function downloadSoftware() {
            window.open('./lifegame-commercial.html', '_blank');
        }
        
        function showPricing() {
            alert('价格信息:\\n\\n试用版: 免费 (30天)\\n标准版: ¥199/年\\n高级版: ¥399/年\\n终身版: ¥999 (一次性)\\n\\n联系方式: <EMAIL>');
        }
    </script>
</body>
</html>`;
        
        fs.writeFileSync(path.join(this.outputDir, 'installer.html'), installerScript);
        console.log('  ✓ 安装程序: installer.html');
    }

    // 生成文档
    async generateDocumentation() {
        console.log('📚 生成文档...');
        
        const readme = `# LifeGame 商业版

## 版本信息
- 版本: ${this.config.version}
- 构建日期: ${this.config.buildDate}
- 保护级别: ${this.config.protectionLevel}

## 文件说明
- \`lifegame-commercial.html\`: 主程序文件（已集成保护系统）
- \`installer.html\`: 安装和演示程序
- \`config.json\`: 构建配置文件
- \`*-keys.csv\`: 许可证密钥文件
- \`test-keys.txt\`: 测试密钥

## 测试密钥
- 试用版: TEST-TRIAL-2024
- 标准版: TEST-STANDARD-2024  
- 高级版: TEST-PREMIUM-2024
- 终身版: TEST-LIFETIME-2024

## 快速开始
1. 打开 \`installer.html\` 查看安装指南
2. 运行 \`lifegame-commercial.html\` 启动软件
3. 使用测试密钥激活相应功能
4. 体验完整的商业版功能

## 保护功能
- ✅ 许可证验证和硬件绑定
- ✅ 反调试和代码保护
- ✅ 字符串加密和混淆
- ✅ 试用版自动生成
- ✅ 多版本功能控制

## 技术支持
如有问题请联系技术支持团队。
        `;
        
        fs.writeFileSync(path.join(this.outputDir, 'README.md'), readme);
        console.log('  ✓ README.md');
    }

    // 显示构建统计
    showBuildStats() {
        console.log('\n📊 构建统计:');
        
        const files = fs.readdirSync(this.outputDir);
        files.forEach(file => {
            const filePath = path.join(this.outputDir, file);
            const stats = fs.statSync(filePath);
            const size = (stats.size / 1024).toFixed(2);
            console.log(`  ${file}: ${size} KB`);
        });
        
        console.log('\n🎉 构建完成！您可以：');
        console.log('  1. 打开 dist/installer.html 查看安装指南');
        console.log('  2. 运行 dist/lifegame-commercial.html 测试软件');
        console.log('  3. 使用 test-keys.txt 中的密钥进行测试');
    }
}

// 命令行接口
if (require.main === module) {
    const builder = new CommercialBuilder();
    
    // 解析命令行参数
    const args = process.argv.slice(2);
    args.forEach(arg => {
        if (arg.startsWith('--version=')) {
            builder.config.version = arg.split('=')[1];
        } else if (arg.startsWith('--protection=')) {
            builder.config.protectionLevel = arg.split('=')[1];
        }
    });
    
    // 开始构建
    builder.build().catch(error => {
        console.error('构建失败:', error);
        process.exit(1);
    });
}

module.exports = CommercialBuilder;
