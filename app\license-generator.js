/**
 * LifeGame 许可证生成器
 * 用于生成和管理软件许可证
 * 注意：此文件仅用于开发和销售方使用，不应包含在最终分发的软件中
 */

class LicenseGenerator {
    constructor() {
        this.secretSalt = 'LifeGame2024Secret';
        this.validKeyPrefixes = {
            'LIFEGAME-TRIAL': { days: 30, type: 'trial', features: ['basic'] },
            'LIFEGAME-STANDARD': { days: 365, type: 'standard', features: ['basic', 'advanced'] },
            'LIFEGAME-PREMIUM': { days: 365, type: 'premium', features: ['basic', 'advanced', 'premium'] },
            'LIFEGAME-LIFETIME': { days: 36500, type: 'lifetime', features: ['basic', 'advanced', 'premium', 'lifetime'] }
        };
    }

    // 生成许可证密钥
    generateLicenseKey(type = 'standard', customSuffix = null) {
        const prefix = this.getKeyPrefix(type);
        if (!prefix) {
            throw new Error('Invalid license type');
        }

        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2, 8).toUpperCase();
        const suffix = customSuffix || random;
        
        const key = `${prefix}-${timestamp}-${suffix}`;
        return key;
    }

    // 获取密钥前缀
    getKeyPrefix(type) {
        const prefixes = {
            'trial': 'LIFEGAME-TRIAL',
            'standard': 'LIFEGAME-STANDARD',
            'premium': 'LIFEGAME-PREMIUM',
            'lifetime': 'LIFEGAME-LIFETIME'
        };
        return prefixes[type];
    }

    // 验证许可证密钥格式
    validateKeyFormat(licenseKey) {
        const keyPattern = /^LIFEGAME-(TRIAL|STANDARD|PREMIUM|LIFETIME)-[a-z0-9]+-[A-Z0-9]+$/;
        return keyPattern.test(licenseKey);
    }

    // 获取密钥信息
    getKeyInfo(licenseKey) {
        if (!this.validateKeyFormat(licenseKey)) {
            throw new Error('Invalid license key format');
        }

        const parts = licenseKey.split('-');
        const type = parts[1].toLowerCase();
        const timestamp = parts[2];
        const suffix = parts[3];

        const keyInfo = this.validKeyPrefixes[`LIFEGAME-${parts[1]}`];
        if (!keyInfo) {
            throw new Error('Unknown license type');
        }

        return {
            type: keyInfo.type,
            days: keyInfo.days,
            features: keyInfo.features,
            generatedAt: parseInt(timestamp, 36),
            suffix: suffix
        };
    }

    // 生成硬件指纹（模拟客户端逻辑）
    async generateHardwareFingerprint(components) {
        const fingerprint = await this.simpleHash(components.join('|'));
        return fingerprint;
    }

    // 简单哈希函数
    async simpleHash(str) {
        const encoder = new TextEncoder();
        const data = encoder.encode(str);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 16);
    }

    // 生成许可证签名
    async generateLicenseSignature(licenseData) {
        const signatureData = `${licenseData.key}|${licenseData.fingerprint}|${licenseData.expiry}|${btoa(this.secretSalt).split('').reverse().join('')}`;
        return await this.simpleHash(signatureData);
    }

    // 创建完整的许可证数据
    async createLicenseData(licenseKey, hardwareFingerprint, customExpiry = null) {
        const keyInfo = this.getKeyInfo(licenseKey);
        const expiry = customExpiry || (Date.now() + (keyInfo.days * 24 * 60 * 60 * 1000));

        const licenseData = {
            key: licenseKey,
            fingerprint: hardwareFingerprint,
            expiry: expiry,
            type: keyInfo.type,
            features: keyInfo.features,
            generatedAt: Date.now(),
            signature: null
        };

        licenseData.signature = await this.generateLicenseSignature(licenseData);
        return licenseData;
    }

    // 批量生成许可证密钥
    generateBatchKeys(type, count) {
        const keys = [];
        for (let i = 0; i < count; i++) {
            keys.push(this.generateLicenseKey(type));
        }
        return keys;
    }

    // 导出许可证密钥列表（用于销售）
    exportKeyList(keys, format = 'json') {
        const keyData = keys.map(key => ({
            key: key,
            info: this.getKeyInfo(key),
            status: 'unused'
        }));

        switch (format) {
            case 'json':
                return JSON.stringify(keyData, null, 2);
            case 'csv':
                const headers = 'Key,Type,Days,Features,Status\n';
                const rows = keyData.map(item => 
                    `${item.key},${item.info.type},${item.info.days},"${item.info.features.join(';')}",${item.status}`
                ).join('\n');
                return headers + rows;
            case 'txt':
                return keyData.map(item => item.key).join('\n');
            default:
                throw new Error('Unsupported export format');
        }
    }

    // 验证许可证完整性
    async validateLicense(licenseData) {
        try {
            // 检查必要字段
            if (!licenseData.key || !licenseData.fingerprint || !licenseData.expiry || !licenseData.signature) {
                return { valid: false, reason: 'Missing required fields' };
            }

            // 验证密钥格式
            if (!this.validateKeyFormat(licenseData.key)) {
                return { valid: false, reason: 'Invalid key format' };
            }

            // 检查过期时间
            if (Date.now() > licenseData.expiry) {
                return { valid: false, reason: 'License expired' };
            }

            // 验证签名
            const expectedSignature = await this.generateLicenseSignature(licenseData);
            if (licenseData.signature !== expectedSignature) {
                return { valid: false, reason: 'Invalid signature' };
            }

            return { valid: true, reason: 'License is valid' };
        } catch (error) {
            return { valid: false, reason: 'Validation error: ' + error.message };
        }
    }

    // 生成激活码（用于离线激活）
    async generateActivationCode(licenseKey, hardwareFingerprint) {
        const keyInfo = this.getKeyInfo(licenseKey);
        const activationData = {
            key: licenseKey,
            fingerprint: hardwareFingerprint,
            timestamp: Date.now()
        };

        const activationString = btoa(JSON.stringify(activationData));
        const checksum = await this.simpleHash(activationString + this.secretSalt);
        
        return `${activationString}.${checksum}`;
    }

    // 验证激活码
    async validateActivationCode(activationCode) {
        try {
            const [activationString, checksum] = activationCode.split('.');
            
            // 验证校验和
            const expectedChecksum = await this.simpleHash(activationString + this.secretSalt);
            if (checksum !== expectedChecksum) {
                throw new Error('Invalid activation code checksum');
            }

            const activationData = JSON.parse(atob(activationString));
            
            // 验证激活码是否过期（24小时有效期）
            if (Date.now() - activationData.timestamp > 24 * 60 * 60 * 1000) {
                throw new Error('Activation code expired');
            }

            return activationData;
        } catch (error) {
            throw new Error('Invalid activation code: ' + error.message);
        }
    }

    // 生成试用许可证
    async generateTrialLicense(hardwareFingerprint, days = 30) {
        const trialKey = this.generateLicenseKey('trial');
        const expiry = Date.now() + (days * 24 * 60 * 60 * 1000);
        
        return await this.createLicenseData(trialKey, hardwareFingerprint, expiry);
    }

    // 许可证统计信息
    generateLicenseStats(licenses) {
        const stats = {
            total: licenses.length,
            byType: {},
            active: 0,
            expired: 0,
            expiringSoon: 0 // 30天内过期
        };

        const now = Date.now();
        const thirtyDaysFromNow = now + (30 * 24 * 60 * 60 * 1000);

        licenses.forEach(license => {
            // 按类型统计
            if (!stats.byType[license.type]) {
                stats.byType[license.type] = 0;
            }
            stats.byType[license.type]++;

            // 按状态统计
            if (license.expiry < now) {
                stats.expired++;
            } else {
                stats.active++;
                if (license.expiry < thirtyDaysFromNow) {
                    stats.expiringSoon++;
                }
            }
        });

        return stats;
    }
}

// 使用示例和测试函数
class LicenseGeneratorDemo {
    static async runDemo() {
        const generator = new LicenseGenerator();
        
        console.log('=== LifeGame License Generator Demo ===\n');

        // 1. 生成不同类型的许可证密钥
        console.log('1. 生成许可证密钥:');
        const trialKey = generator.generateLicenseKey('trial');
        const standardKey = generator.generateLicenseKey('standard');
        const premiumKey = generator.generateLicenseKey('premium');
        const lifetimeKey = generator.generateLicenseKey('lifetime');
        
        console.log(`试用版: ${trialKey}`);
        console.log(`标准版: ${standardKey}`);
        console.log(`高级版: ${premiumKey}`);
        console.log(`终身版: ${lifetimeKey}\n`);

        // 2. 模拟硬件指纹
        const mockFingerprint = await generator.generateHardwareFingerprint([
            '1920x1080x24',
            'Asia/Shanghai',
            'zh-CN',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
            'mock-canvas-fingerprint',
            'mock-webgl-renderer'
        ]);
        console.log(`2. 模拟硬件指纹: ${mockFingerprint}\n`);

        // 3. 创建完整许可证数据
        console.log('3. 创建许可证数据:');
        const licenseData = await generator.createLicenseData(standardKey, mockFingerprint);
        console.log(JSON.stringify(licenseData, null, 2));
        console.log();

        // 4. 验证许可证
        console.log('4. 验证许可证:');
        const validation = await generator.validateLicense(licenseData);
        console.log(`验证结果: ${validation.valid ? '有效' : '无效'} - ${validation.reason}\n`);

        // 5. 生成激活码
        console.log('5. 生成激活码:');
        const activationCode = await generator.generateActivationCode(standardKey, mockFingerprint);
        console.log(`激活码: ${activationCode}\n`);

        // 6. 批量生成密钥
        console.log('6. 批量生成密钥:');
        const batchKeys = generator.generateBatchKeys('standard', 5);
        console.log('批量密钥:', batchKeys);
        console.log();

        // 7. 导出密钥列表
        console.log('7. 导出密钥列表 (CSV格式):');
        const csvExport = generator.exportKeyList(batchKeys, 'csv');
        console.log(csvExport);
    }
}

// 如果在Node.js环境中运行演示
if (typeof window === 'undefined' && typeof require !== 'undefined') {
    // Node.js环境
    const crypto = require('crypto');
    global.crypto = {
        subtle: {
            digest: async (algorithm, data) => {
                const hash = crypto.createHash('sha256');
                hash.update(data);
                return hash.digest();
            }
        }
    };
    global.TextEncoder = require('util').TextEncoder;
    global.btoa = (str) => Buffer.from(str).toString('base64');
    global.atob = (str) => Buffer.from(str, 'base64').toString();
    
    // 运行演示
    LicenseGeneratorDemo.runDemo().catch(console.error);
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LicenseGenerator, LicenseGeneratorDemo };
}
