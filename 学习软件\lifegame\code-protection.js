/**
 * LifeGame 代码保护模块
 * 功能: 代码混淆、加密、反调试保护
 */

class CodeProtection {
    constructor() {
        this.isProtected = true;
        this.debuggerDetected = false;
        this.tamperingDetected = false;
        this.originalFunctions = new Map();
        
        this.init();
    }

    // 初始化保护机制
    init() {
        this.setupAntiDebug();
        this.setupIntegrityCheck();
        this.setupFunctionProtection();
        this.setupConsoleProtection();
        this.obfuscateGlobalVariables();
    }

    // 反调试保护
    setupAntiDebug() {
        // 方法1: 检测调试器
        setInterval(() => {
            const start = performance.now();
            debugger;
            const end = performance.now();
            
            if (end - start > 100) {
                this.handleDebuggerDetection();
            }
        }, 1000);

        // 方法2: 检测开发者工具
        let devtools = false;
        setInterval(() => {
            if (window.outerHeight - window.innerHeight > 200 || 
                window.outerWidth - window.innerWidth > 200) {
                if (!devtools) {
                    devtools = true;
                    this.handleDevToolsDetection();
                }
            } else {
                devtools = false;
            }
        }, 500);

        // 方法3: 检测控制台
        let consoleOpened = false;
        Object.defineProperty(window, 'console', {
            get: function() {
                if (!consoleOpened) {
                    consoleOpened = true;
                    setTimeout(() => {
                        window.codeProtection.handleConsoleDetection();
                    }, 100);
                }
                return console;
            }
        });
    }

    // 完整性检查
    setupIntegrityCheck() {
        // 检查关键函数是否被修改
        const criticalFunctions = [
            'localStorage.setItem',
            'localStorage.getItem',
            'JSON.parse',
            'JSON.stringify',
            'atob',
            'btoa'
        ];

        criticalFunctions.forEach(funcPath => {
            const func = this.getNestedProperty(window, funcPath);
            if (func && typeof func === 'function') {
                this.originalFunctions.set(funcPath, func.toString());
            }
        });

        // 定期检查
        setInterval(() => {
            this.checkFunctionIntegrity();
        }, 5000);
    }

    // 检查函数完整性
    checkFunctionIntegrity() {
        for (const [funcPath, originalCode] of this.originalFunctions) {
            const currentFunc = this.getNestedProperty(window, funcPath);
            if (currentFunc && currentFunc.toString() !== originalCode) {
                this.handleTamperingDetection(`Function ${funcPath} has been modified`);
                break;
            }
        }
    }

    // 获取嵌套属性
    getNestedProperty(obj, path) {
        return path.split('.').reduce((current, prop) => current && current[prop], obj);
    }

    // 函数保护
    setupFunctionProtection() {
        // 保护关键的LifeGame函数
        const protectedFunctions = [
            'calculateAttributes',
            'saveData',
            'loadData',
            'addDailyRecord'
        ];

        protectedFunctions.forEach(funcName => {
            if (window[funcName]) {
                this.protectFunction(funcName);
            }
        });
    }

    // 保护单个函数
    protectFunction(funcName) {
        const originalFunc = window[funcName];
        if (typeof originalFunc !== 'function') return;

        window[funcName] = function(...args) {
            // 检查调用栈
            if (!window.codeProtection.isLegitimateCall()) {
                console.warn(`Unauthorized call to ${funcName}`);
                return null;
            }

            // 检查许可证状态
            if (!window.lifegameProtection || !window.lifegameProtection.isLicenseValid()) {
                console.warn(`License required for ${funcName}`);
                return null;
            }

            return originalFunc.apply(this, args);
        };

        // 隐藏保护痕迹
        Object.defineProperty(window[funcName], 'toString', {
            value: () => originalFunc.toString()
        });
    }

    // 检查是否为合法调用
    isLegitimateCall() {
        const stack = new Error().stack;
        
        // 检查调用栈中是否包含可疑的调用
        const suspiciousPatterns = [
            'eval',
            'Function',
            'setTimeout',
            'setInterval'
        ];

        for (const pattern of suspiciousPatterns) {
            if (stack.includes(pattern)) {
                return false;
            }
        }

        return true;
    }

    // 控制台保护
    setupConsoleProtection() {
        // 重写console方法
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;

        console.log = function(...args) {
            if (window.codeProtection.shouldBlockConsole(args)) {
                return;
            }
            originalLog.apply(console, args);
        };

        console.warn = function(...args) {
            if (window.codeProtection.shouldBlockConsole(args)) {
                return;
            }
            originalWarn.apply(console, args);
        };

        console.error = function(...args) {
            if (window.codeProtection.shouldBlockConsole(args)) {
                return;
            }
            originalError.apply(console, args);
        };
    }

    // 判断是否应该阻止控制台输出
    shouldBlockConsole(args) {
        const sensitiveKeywords = [
            'license',
            'protection',
            'fingerprint',
            'activation',
            'decrypt'
        ];

        const argsString = args.join(' ').toLowerCase();
        return sensitiveKeywords.some(keyword => argsString.includes(keyword));
    }

    // 混淆全局变量
    obfuscateGlobalVariables() {
        // 创建混淆的变量名
        const obfuscatedNames = this.generateObfuscatedNames();
        
        // 移动敏感的全局变量到混淆的命名空间
        window[obfuscatedNames.protection] = window.lifegameProtection;
        window[obfuscatedNames.data] = window.data;
        
        // 删除原始引用（在开发完成后）
        // delete window.lifegameProtection;
        // delete window.data;
    }

    // 生成混淆的变量名
    generateObfuscatedNames() {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const generateName = (length = 8) => {
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        };

        return {
            protection: generateName(),
            data: generateName(),
            license: generateName()
        };
    }

    // 处理调试器检测
    handleDebuggerDetection() {
        this.debuggerDetected = true;
        console.warn('Debugger detected');
        
        // 随机执行保护措施
        if (Math.random() < 0.3) {
            this.executeProtectionMeasure('debugger');
        }
    }

    // 处理开发者工具检测
    handleDevToolsDetection() {
        console.warn('Developer tools detected');
        
        if (Math.random() < 0.2) {
            this.executeProtectionMeasure('devtools');
        }
    }

    // 处理控制台检测
    handleConsoleDetection() {
        console.warn('Console access detected');
        
        if (Math.random() < 0.1) {
            this.executeProtectionMeasure('console');
        }
    }

    // 处理篡改检测
    handleTamperingDetection(details) {
        this.tamperingDetected = true;
        console.error('Code tampering detected:', details);
        
        this.executeProtectionMeasure('tampering');
    }

    // 执行保护措施
    executeProtectionMeasure(type) {
        const measures = {
            debugger: () => {
                // 显示警告信息
                this.showProtectionWarning('检测到调试器，请关闭开发者工具。');
            },
            devtools: () => {
                // 模糊页面内容
                this.blurPageContent();
            },
            console: () => {
                // 清空控制台
                if (console.clear) console.clear();
            },
            tampering: () => {
                // 重新加载页面
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        };

        const measure = measures[type];
        if (measure) {
            measure();
        }
    }

    // 显示保护警告
    showProtectionWarning(message) {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(255,0,0,0.1); z-index: 9999; display: flex;
            align-items: center; justify-content: center; color: red;
            font-family: Arial, sans-serif; font-size: 16px; text-align: center;
            pointer-events: none;
        `;
        overlay.innerHTML = `<div style="background: rgba(255,255,255,0.9); padding: 20px; border-radius: 10px; border: 2px solid red;">
            <h3>⚠️ 安全警告</h3>
            <p>${message}</p>
        </div>`;
        document.body.appendChild(overlay);

        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 5000);
    }

    // 模糊页面内容
    blurPageContent() {
        document.body.style.filter = 'blur(5px)';
        document.body.style.pointerEvents = 'none';
        
        setTimeout(() => {
            document.body.style.filter = '';
            document.body.style.pointerEvents = '';
        }, 3000);
    }

    // 代码混淆工具函数
    static obfuscateString(str) {
        // 简单的字符串混淆
        return btoa(str).split('').reverse().join('');
    }

    static deobfuscateString(obfuscatedStr) {
        // 解混淆
        return atob(obfuscatedStr.split('').reverse().join(''));
    }

    // 动态代码执行保护
    static protectEval() {
        const originalEval = window.eval;
        window.eval = function(code) {
            console.warn('eval() execution blocked for security');
            return null;
        };

        const originalFunction = window.Function;
        window.Function = function(...args) {
            console.warn('Function() constructor blocked for security');
            return function() {};
        };
    }

    // 获取保护状态
    getProtectionStatus() {
        return {
            isProtected: this.isProtected,
            debuggerDetected: this.debuggerDetected,
            tamperingDetected: this.tamperingDetected,
            timestamp: Date.now()
        };
    }
}

// 代码混淆辅助工具
class CodeObfuscator {
    // 混淆JavaScript代码
    static obfuscateCode(code) {
        // 简单的变量名混淆
        const variableMap = new Map();
        let counter = 0;
        
        // 生成混淆的变量名
        const generateObfuscatedName = () => {
            const chars = 'abcdefghijklmnopqrstuvwxyz';
            let name = '';
            let num = counter++;
            do {
                name = chars[num % 26] + name;
                num = Math.floor(num / 26);
            } while (num > 0);
            return '_' + name;
        };

        // 替换变量名
        let obfuscatedCode = code;
        const variablePattern = /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\b/g;
        
        obfuscatedCode = obfuscatedCode.replace(variablePattern, (match) => {
            // 跳过JavaScript关键字和内置对象
            const keywords = ['var', 'let', 'const', 'function', 'if', 'else', 'for', 'while', 'return', 'console', 'window', 'document'];
            if (keywords.includes(match)) {
                return match;
            }
            
            if (!variableMap.has(match)) {
                variableMap.set(match, generateObfuscatedName());
            }
            return variableMap.get(match);
        });

        return obfuscatedCode;
    }

    // 字符串加密
    static encryptStrings(code) {
        const stringPattern = /(['"`])((?:(?!\1)[^\\]|\\.)*)(\1)/g;
        
        return code.replace(stringPattern, (match, quote, content, endQuote) => {
            if (content.length > 3) { // 只加密较长的字符串
                const encrypted = CodeProtection.obfuscateString(content);
                return `CodeProtection.deobfuscateString('${encrypted}')`;
            }
            return match;
        });
    }
}

// 初始化代码保护
window.codeProtection = new CodeProtection();

// 保护eval和Function构造函数
CodeProtection.protectEval();

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CodeProtection, CodeObfuscator };
}
