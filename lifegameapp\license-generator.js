/**
 * LifeGame 许可证生成器
 * 用于生成和管理软件许可证
 * 注意：此文件仅用于开发和销售方使用，不应包含在最终分发的软件中
 */

class LicenseGenerator {
    constructor() {
        this.secretSalt = 'LifeGame2024Secret';
        this.validKeyPrefixes = {
            'LIFEGAME-TRIAL': { days: 7, type: 'trial', features: ['basic'] }, // 改成7天试用
            'LIFEGAME-STANDARD': { days: 365, type: 'standard', features: ['basic', 'advanced'] },
            'LIFEGAME-PREMIUM': { days: 365, type: 'premium', features: ['basic', 'advanced', 'premium'] },
            'LIFEGAME-LIFETIME': { days: 36500, type: 'lifetime', features: ['basic', 'advanced', 'premium', 'lifetime'] }
        };
    }

    // 生成许可证密钥
    generateLicenseKey(type = 'standard', customSuffix = null) {
        const prefix = this.getKeyPrefix(type);
        if (!prefix) {
            throw new Error('Invalid license type');
        }

        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2, 8).toUpperCase();
        const suffix = customSuffix || random;
        
        const key = `${prefix}-${timestamp}-${suffix}`;
        return key;
    }

    // 获取密钥前缀
    getKeyPrefix(type) {
        const prefixes = {
            'trial': 'LIFEGAME-TRIAL',
            'standard': 'LIFEGAME-STANDARD',
            'premium': 'LIFEGAME-PREMIUM',
            'lifetime': 'LIFEGAME-LIFETIME'
        };
        return prefixes[type];
    }

    // 验证许可证密钥格式
    validateKeyFormat(licenseKey) {
        const keyPattern = /^LIFEGAME-(TRIAL|STANDARD|PREMIUM|LIFETIME)-[a-z0-9]+-[A-Z0-9]+$/;
        return keyPattern.test(licenseKey);
    }

    // 获取密钥信息
    getKeyInfo(licenseKey) {
        if (!this.validateKeyFormat(licenseKey)) {
            throw new Error('Invalid license key format');
        }

        const parts = licenseKey.split('-');
        const type = parts[1].toLowerCase();
        const timestamp = parts[2];
        const suffix = parts[3];

        const keyInfo = this.validKeyPrefixes[`LIFEGAME-${parts[1]}`];
        if (!keyInfo) {
            throw new Error('Unknown license type');
        }

        return {
            type: keyInfo.type,
            days: keyInfo.days,
            features: keyInfo.features,
            generatedAt: parseInt(timestamp, 36),
            suffix: suffix
        };
    }

    // 批量生成许可证密钥
    generateBatchKeys(type, count) {
        const keys = [];
        for (let i = 0; i < count; i++) {
            keys.push(this.generateLicenseKey(type));
        }
        return keys;
    }

    // 导出许可证密钥列表（用于销售）
    exportKeyList(keys, format = 'json') {
        const keyData = keys.map(key => ({
            key: key,
            info: this.getKeyInfo(key),
            status: 'unused'
        }));

        switch (format) {
            case 'json':
                return JSON.stringify(keyData, null, 2);
            case 'csv':
                const headers = 'Key,Type,Days,Features,Status\n';
                const rows = keyData.map(item => 
                    `${item.key},${item.info.type},${item.info.days},"${item.info.features.join(';')}",${item.status}`
                ).join('\n');
                return headers + rows;
            case 'txt':
                return keyData.map(item => item.key).join('\n');
            default:
                throw new Error('Unsupported export format');
        }
    }

    // 生成测试密钥
    generateTestKeys() {
        return {
            trial: 'TEST-TRIAL-2024',
            standard: 'TEST-STANDARD-2024',
            premium: 'TEST-PREMIUM-2024',
            lifetime: 'TEST-LIFETIME-2024'
        };
    }
}

// 使用示例和测试函数
class LicenseGeneratorDemo {
    static runDemo() {
        const generator = new LicenseGenerator();
        
        console.log('=== LifeGame License Generator Demo ===\n');

        // 1. 生成不同类型的许可证密钥
        console.log('1. 生成许可证密钥:');
        const trialKey = generator.generateLicenseKey('trial');
        const standardKey = generator.generateLicenseKey('standard');
        const premiumKey = generator.generateLicenseKey('premium');
        const lifetimeKey = generator.generateLicenseKey('lifetime');
        
        console.log(`试用版: ${trialKey}`);
        console.log(`标准版: ${standardKey}`);
        console.log(`高级版: ${premiumKey}`);
        console.log(`终身版: ${lifetimeKey}\n`);

        // 2. 生成测试密钥
        console.log('2. 测试密钥:');
        const testKeys = generator.generateTestKeys();
        Object.entries(testKeys).forEach(([type, key]) => {
            console.log(`${type}: ${key}`);
        });
        console.log();

        // 3. 批量生成密钥
        console.log('3. 批量生成密钥:');
        const batchKeys = generator.generateBatchKeys('standard', 5);
        console.log('批量密钥:', batchKeys);
        console.log();

        // 4. 导出密钥列表
        console.log('4. 导出密钥列表 (CSV格式):');
        const csvExport = generator.exportKeyList(batchKeys, 'csv');
        console.log(csvExport);

        return {
            testKeys,
            batchKeys,
            csvExport
        };
    }
}

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.LicenseGenerator = LicenseGenerator;
    window.LicenseGeneratorDemo = LicenseGeneratorDemo;
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LicenseGenerator, LicenseGeneratorDemo };
}
