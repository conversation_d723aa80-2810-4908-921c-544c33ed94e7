/**
 * LifeGame 用户认证系统
 * 功能: 用户身份验证、授权管理、会话控制
 */

class UserAuthentication {
    constructor() {
        this.isAuthenticated = false;
        this.currentUser = null;
        this.sessionToken = null;
        this.authConfig = {
            sessionTimeout: 24 * 60 * 60 * 1000, // 24小时
            maxLoginAttempts: 5,
            lockoutDuration: 30 * 60 * 1000 // 30分钟
        };
        
        this.init();
    }

    // 初始化认证系统
    init() {
        this.loadStoredSession();
        this.setupSessionMonitoring();
        this.setupSecurityHeaders();
    }

    // 加载存储的会话
    loadStoredSession() {
        try {
            const storedSession = localStorage.getItem('lifegame_session');
            if (storedSession) {
                const sessionData = JSON.parse(atob(storedSession));
                
                if (this.validateSession(sessionData)) {
                    this.isAuthenticated = true;
                    this.currentUser = sessionData.user;
                    this.sessionToken = sessionData.token;
                    this.updateLastActivity();
                }
            }
        } catch (error) {
            console.error('Failed to load stored session:', error);
            this.clearSession();
        }
    }

    // 验证会话
    validateSession(sessionData) {
        if (!sessionData.token || !sessionData.user || !sessionData.expiry) {
            return false;
        }

        // 检查会话是否过期
        if (Date.now() > sessionData.expiry) {
            return false;
        }

        // 验证会话令牌
        return this.validateSessionToken(sessionData.token, sessionData.user);
    }

    // 验证会话令牌
    validateSessionToken(token, user) {
        try {
            // 简单的令牌验证（实际应用中应该更复杂）
            const expectedToken = this.generateSessionToken(user);
            return token === expectedToken;
        } catch (error) {
            return false;
        }
    }

    // 生成会话令牌
    generateSessionToken(user) {
        const tokenData = `${user.id}|${user.email}|${Date.now()}|${this.getSecretKey()}`;
        return btoa(tokenData).replace(/[+/=]/g, ''); // 移除特殊字符
    }

    // 获取密钥
    getSecretKey() {
        // 在实际应用中，这应该是一个更安全的密钥
        return 'LifeGameSecretKey2024';
    }

    // 用户登录
    async login(credentials) {
        try {
            // 检查登录尝试次数
            if (this.isAccountLocked(credentials.email)) {
                throw new Error('账户已被锁定，请稍后再试');
            }

            // 验证凭据
            const user = await this.validateCredentials(credentials);
            
            if (user) {
                // 创建会话
                await this.createSession(user);
                
                // 清除登录失败记录
                this.clearLoginAttempts(credentials.email);
                
                return { success: true, user: user };
            } else {
                // 记录登录失败
                this.recordLoginAttempt(credentials.email);
                throw new Error('用户名或密码错误');
            }
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 验证用户凭据
    async validateCredentials(credentials) {
        // 在实际应用中，这里应该连接到用户数据库
        // 这里使用模拟的用户数据
        const users = await this.getRegisteredUsers();
        
        const user = users.find(u => 
            u.email === credentials.email && 
            this.verifyPassword(credentials.password, u.passwordHash)
        );
        
        return user || null;
    }

    // 获取注册用户（模拟）
    async getRegisteredUsers() {
        // 在实际应用中，这些数据应该来自安全的后端
        const storedUsers = localStorage.getItem('lifegame_users');
        if (storedUsers) {
            return JSON.parse(atob(storedUsers));
        }
        
        return []; // 空用户列表
    }

    // 验证密码
    verifyPassword(password, hash) {
        // 简单的密码验证（实际应该使用bcrypt等）
        return this.hashPassword(password) === hash;
    }

    // 密码哈希
    hashPassword(password) {
        // 简单的哈希函数（实际应该使用更安全的方法）
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString(16);
    }

    // 创建会话
    async createSession(user) {
        const sessionData = {
            user: {
                id: user.id,
                email: user.email,
                name: user.name,
                licenseType: user.licenseType
            },
            token: this.generateSessionToken(user),
            expiry: Date.now() + this.authConfig.sessionTimeout,
            createdAt: Date.now(),
            lastActivity: Date.now()
        };

        // 存储会话
        localStorage.setItem('lifegame_session', btoa(JSON.stringify(sessionData)));
        
        this.isAuthenticated = true;
        this.currentUser = sessionData.user;
        this.sessionToken = sessionData.token;
    }

    // 用户注册
    async register(userData) {
        try {
            // 验证注册数据
            this.validateRegistrationData(userData);
            
            // 检查用户是否已存在
            const existingUsers = await this.getRegisteredUsers();
            if (existingUsers.find(u => u.email === userData.email)) {
                throw new Error('该邮箱已被注册');
            }
            
            // 创建新用户
            const newUser = {
                id: this.generateUserId(),
                email: userData.email,
                name: userData.name,
                passwordHash: this.hashPassword(userData.password),
                licenseType: userData.licenseType || 'trial',
                registeredAt: Date.now(),
                isActive: true
            };
            
            // 保存用户
            existingUsers.push(newUser);
            localStorage.setItem('lifegame_users', btoa(JSON.stringify(existingUsers)));
            
            return { success: true, user: newUser };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 验证注册数据
    validateRegistrationData(userData) {
        if (!userData.email || !this.isValidEmail(userData.email)) {
            throw new Error('请输入有效的邮箱地址');
        }
        
        if (!userData.password || userData.password.length < 6) {
            throw new Error('密码长度至少为6位');
        }
        
        if (!userData.name || userData.name.trim().length < 2) {
            throw new Error('请输入有效的姓名');
        }
    }

    // 验证邮箱格式
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // 生成用户ID
    generateUserId() {
        return 'user_' + Date.now().toString(36) + '_' + Math.random().toString(36).substring(2);
    }

    // 用户登出
    logout() {
        this.clearSession();
        this.isAuthenticated = false;
        this.currentUser = null;
        this.sessionToken = null;
    }

    // 清除会话
    clearSession() {
        localStorage.removeItem('lifegame_session');
    }

    // 检查账户是否被锁定
    isAccountLocked(email) {
        const lockoutData = this.getLoginAttempts(email);
        if (lockoutData.attempts >= this.authConfig.maxLoginAttempts) {
            const timeSinceLastAttempt = Date.now() - lockoutData.lastAttempt;
            return timeSinceLastAttempt < this.authConfig.lockoutDuration;
        }
        return false;
    }

    // 记录登录尝试
    recordLoginAttempt(email) {
        const attempts = this.getLoginAttempts(email);
        attempts.attempts++;
        attempts.lastAttempt = Date.now();
        
        localStorage.setItem(`lifegame_attempts_${email}`, JSON.stringify(attempts));
    }

    // 获取登录尝试记录
    getLoginAttempts(email) {
        const stored = localStorage.getItem(`lifegame_attempts_${email}`);
        if (stored) {
            return JSON.parse(stored);
        }
        return { attempts: 0, lastAttempt: 0 };
    }

    // 清除登录尝试记录
    clearLoginAttempts(email) {
        localStorage.removeItem(`lifegame_attempts_${email}`);
    }

    // 更新最后活动时间
    updateLastActivity() {
        if (this.isAuthenticated) {
            try {
                const sessionData = JSON.parse(atob(localStorage.getItem('lifegame_session')));
                sessionData.lastActivity = Date.now();
                localStorage.setItem('lifegame_session', btoa(JSON.stringify(sessionData)));
            } catch (error) {
                console.error('Failed to update last activity:', error);
            }
        }
    }

    // 设置会话监控
    setupSessionMonitoring() {
        // 定期检查会话状态
        setInterval(() => {
            if (this.isAuthenticated) {
                this.checkSessionExpiry();
                this.updateLastActivity();
            }
        }, 60000); // 每分钟检查一次

        // 监听页面活动
        document.addEventListener('click', () => this.updateLastActivity());
        document.addEventListener('keypress', () => this.updateLastActivity());
    }

    // 检查会话过期
    checkSessionExpiry() {
        try {
            const sessionData = JSON.parse(atob(localStorage.getItem('lifegame_session')));
            if (Date.now() > sessionData.expiry) {
                this.handleSessionExpiry();
            }
        } catch (error) {
            this.handleSessionExpiry();
        }
    }

    // 处理会话过期
    handleSessionExpiry() {
        this.logout();
        this.showSessionExpiredDialog();
    }

    // 显示会话过期对话框
    showSessionExpiredDialog() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
            align-items: center; justify-content: center; color: white;
            font-family: Arial, sans-serif;
        `;
        overlay.innerHTML = `<div style="background: #333; padding: 40px; border-radius: 10px; max-width: 400px; text-align: center;">
            <h3>会话已过期</h3>
            <p>您的登录会话已过期，请重新登录以继续使用。</p>
            <button onclick="window.userAuth.showLoginDialog(); this.parentElement.parentElement.remove();" 
                    style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                重新登录
            </button>
        </div>`;
        document.body.appendChild(overlay);
    }

    // 设置安全头
    setupSecurityHeaders() {
        // 在实际应用中，这些应该在服务器端设置
        // 这里只是客户端的补充措施
        
        // 防止点击劫持
        if (window.top !== window.self) {
            window.top.location = window.self.location;
        }
    }

    // 显示登录对话框
    showLoginDialog() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.9); z-index: 10000; display: flex;
            align-items: center; justify-content: center; color: white;
            font-family: Arial, sans-serif;
        `;
        overlay.innerHTML = `<div style="background: #333; padding: 40px; border-radius: 10px; max-width: 400px; width: 90%;">
            <h2 style="text-align: center; margin-bottom: 30px;">LifeGame 登录</h2>
            <form id="loginForm">
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px;">邮箱:</label>
                    <input type="email" id="loginEmail" required 
                           style="width: 100%; padding: 10px; border: none; border-radius: 5px; box-sizing: border-box;">
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px;">密码:</label>
                    <input type="password" id="loginPassword" required 
                           style="width: 100%; padding: 10px; border: none; border-radius: 5px; box-sizing: border-box;">
                </div>
                <div style="text-align: center;">
                    <button type="submit" 
                            style="padding: 12px 30px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                        登录
                    </button>
                    <button type="button" onclick="window.userAuth.showRegisterDialog(); this.closest('[style*=\"position: fixed\"]').remove();" 
                            style="padding: 12px 30px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        注册
                    </button>
                </div>
            </form>
        </div>`;
        
        document.body.appendChild(overlay);
        
        // 绑定登录表单事件
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            const result = await window.userAuth.login({ email, password });
            if (result.success) {
                overlay.remove();
                location.reload();
            } else {
                alert('登录失败: ' + result.error);
            }
        });
    }

    // 显示注册对话框
    showRegisterDialog() {
        // 类似的注册对话框实现
        console.log('Show register dialog');
    }

    // 获取当前用户
    getCurrentUser() {
        return this.currentUser;
    }

    // 检查是否已认证
    isUserAuthenticated() {
        return this.isAuthenticated;
    }

    // 检查用户权限
    hasPermission(permission) {
        if (!this.isAuthenticated || !this.currentUser) {
            return false;
        }
        
        // 根据许可证类型检查权限
        const permissions = {
            trial: ['basic'],
            standard: ['basic', 'advanced'],
            premium: ['basic', 'advanced', 'premium'],
            lifetime: ['basic', 'advanced', 'premium', 'lifetime']
        };
        
        const userPermissions = permissions[this.currentUser.licenseType] || [];
        return userPermissions.includes(permission);
    }
}

// 初始化用户认证系统
window.userAuth = new UserAuthentication();

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserAuthentication;
}
