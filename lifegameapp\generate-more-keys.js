/**
 * 生成更多许可证密钥的简单脚本
 * 使用方法：node generate-more-keys.js
 */

const fs = require('fs');
const { LicenseGenerator } = require('./license-generator.js');

console.log('🔑 LifeGame 密钥生成器');
console.log('====================\n');

const generator = new LicenseGenerator();

// 询问用户要生成什么类型的密钥
console.log('请选择要生成的密钥类型：');
console.log('1. 试用版密钥 (7天)');
console.log('2. 标准版密钥 (1年)');
console.log('3. 高级版密钥 (1年)');
console.log('4. 终身版密钥 (永久)');
console.log('5. 生成所有类型\n');

// 由于这是命令行脚本，我们直接生成一些示例
function generateKeys() {
    const keyTypes = [
        { type: 'trial', count: 20, name: '试用版' },
        { type: 'standard', count: 50, name: '标准版' },
        { type: 'premium', count: 30, name: '高级版' },
        { type: 'lifetime', count: 10, name: '终身版' }
    ];

    console.log('正在生成密钥...\n');

    keyTypes.forEach(keyType => {
        console.log(`生成 ${keyType.name} 密钥 (${keyType.count} 个)...`);
        
        // 生成密钥
        const keys = generator.generateBatchKeys(keyType.type, keyType.count);
        
        // 导出为CSV格式
        const csvContent = generator.exportKeyList(keys, 'csv');
        
        // 保存到文件
        const filename = `new-${keyType.type}-keys-${Date.now()}.csv`;
        fs.writeFileSync(filename, csvContent);
        
        console.log(`✓ 已保存到: ${filename}`);
        
        // 显示前3个密钥作为示例
        console.log('示例密钥:');
        keys.slice(0, 3).forEach((key, index) => {
            console.log(`  ${index + 1}. ${key}`);
        });
        console.log('');
    });

    console.log('🎉 密钥生成完成！');
    console.log('\n📋 使用说明：');
    console.log('1. 将生成的CSV文件保存好，这些是您的密钥库');
    console.log('2. 销售时，从CSV文件中取出一个密钥给客户');
    console.log('3. 记录哪些密钥已经售出，避免重复销售');
    console.log('4. 定期备份密钥文件');
}

// 生成单个密钥的函数
function generateSingleKey(type) {
    const key = generator.generateLicenseKey(type);
    const keyInfo = generator.getKeyInfo(key);
    
    console.log(`\n🔑 新生成的${keyInfo.type}密钥：`);
    console.log(`密钥: ${key}`);
    console.log(`类型: ${keyInfo.type}`);
    console.log(`有效期: ${keyInfo.days}天`);
    console.log(`功能: ${keyInfo.features.join(', ')}`);
    
    return key;
}

// 快速生成函数
function quickGenerate() {
    console.log('🚀 快速生成模式\n');
    
    // 生成各种类型的密钥
    const quickKeys = {
        trial: generateSingleKey('trial'),
        standard: generateSingleKey('standard'),
        premium: generateSingleKey('premium'),
        lifetime: generateSingleKey('lifetime')
    };
    
    // 保存到快速密钥文件
    const quickKeysContent = Object.entries(quickKeys).map(([type, key]) => 
        `${type.toUpperCase()}: ${key}`
    ).join('\n');
    
    const quickFilename = `quick-keys-${Date.now()}.txt`;
    fs.writeFileSync(quickFilename, quickKeysContent);
    
    console.log(`\n💾 快速密钥已保存到: ${quickFilename}`);
}

// 主函数
function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--quick')) {
        quickGenerate();
    } else if (args.includes('--batch')) {
        generateKeys();
    } else {
        console.log('使用方法：');
        console.log('  node generate-more-keys.js --quick    # 快速生成各类型密钥各1个');
        console.log('  node generate-more-keys.js --batch    # 批量生成密钥');
        console.log('');
        console.log('默认执行批量生成...\n');
        generateKeys();
    }
}

// 运行主函数
main();
